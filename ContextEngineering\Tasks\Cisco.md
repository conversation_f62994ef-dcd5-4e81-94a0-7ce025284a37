



useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
4
useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13

useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
useBackgroundTimer.tsx:121 ❌ Impossible de créer le Web Worker: Error: Forcer fallback - Web Worker non fiable en développement
    at useBackgroundTimer.tsx:44:13
﻿





























































